const api = require('../utils/requestHelper');
const { describeWithTransaction } = require('../utils/describeWithTransaction');
const { UserFactory, WorkAreaFactory } = require('../factories');
const { WorkArea } = require('../../src/models');

describeWithTransaction('WorkAreasController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();

    // Create test work areas
    await WorkAreaFactory.create({ name: 'Engineering' });
    await WorkAreaFactory.create({ name: 'Product Management' });
    await WorkAreaFactory.create({ name: 'Design' });
  });

  describe('GET /api/v1/work_areas', () => {
    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/work_areas');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/work_areas');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });
    });

    describe('validation', () => {
      it('should return 400 when sort field is invalid', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          sort: 'invalid_field',
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when sort_direction is invalid', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          sort_direction: 'invalid',
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when page is less than 1', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          page: 0,
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should return 400 when limit exceeds maximum', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          limit: 101,
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('successful responses', () => {
      it('should return work areas with correct format', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        const workArea = response.body.data[0];
        expect(workArea).toHaveProperty('id');
        expect(workArea).toHaveProperty('name');
        expect(typeof workArea.id).toBe('number');
        expect(typeof workArea.name).toBe('string');
      });

      it('should support pagination', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          page: 1,
          limit: 2,
        });

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 2,
          total: expect.any(Number),
        });
      });

      it('should support sorting by name ascending', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          sort: 'name',
          sort_direction: 'asc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        // Check if sorted alphabetically
        const names = response.body.data.map(workArea => workArea.name);
        const sortedNames = [...names].sort();
        expect(names).toEqual(sortedNames);
      });

      it('should support sorting by name descending', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          sort: 'name',
          sort_direction: 'desc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        // Check if sorted reverse alphabetically
        const names = response.body.data.map(workArea => workArea.name);
        const sortedNames = [...names].sort().reverse();
        expect(names).toEqual(sortedNames);
      });

      it('should support sorting by id', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          sort: 'id',
          sort_direction: 'asc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        // Check if sorted by id
        const ids = response.body.data.map(workArea => workArea.id);
        const sortedIds = [...ids].sort((a, b) => a - b);
        expect(ids).toEqual(sortedIds);
      });

      it('should support sorting by created_at', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          sort: 'created_at',
          sort_direction: 'desc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);
      });

      it('should support sorting by updated_at', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          sort: 'updated_at',
          sort_direction: 'asc',
        });

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);
      });

      it('should use default sorting when no sort specified', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas');

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThanOrEqual(3);

        // Default sort should be by name ascending
        const names = response.body.data.map(workArea => workArea.name);
        const sortedNames = [...names].sort();
        expect(names).toEqual(sortedNames);
      });

      it('should handle search parameter', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          search: 'neer',
        });

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].name).toBe('Engineering');
      });
    });

    describe('edge cases', () => {
      it('should handle empty results gracefully', async () => {
        // Delete all work areas
        await WorkArea.destroy({ where: {}, force: true });

        const response = await api.as(admin).get('/api/v1/work_areas');

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.total).toBe(0);
      });

      it('should handle large page numbers', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          page: 999,
          limit: 10,
        });

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.page).toBe(999);
      });

      it('should coerce string numbers to integers for pagination', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          page: '2',
          limit: '1',
        });

        expect(response.status).toBe(200);
        expect(response.body.pagination.page).toBe(2);
        expect(response.body.pagination.limit).toBe(1);
      });

      it('should handle negative page numbers by defaulting to 1', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          page: -1,
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle negative limit by validation error', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          limit: -1,
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle zero limit by validation error', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          limit: 0,
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle non-numeric page parameter', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          page: 'invalid',
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle non-numeric limit parameter', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          limit: 'invalid',
        });

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should handle additional properties gracefully', async () => {
        const response = await api.as(admin).get('/api/v1/work_areas', {
          unknown_param: 'value',
        });

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
      });

      it('should return consistent results for same query', async () => {
        const response1 = await api.as(admin).get('/api/v1/work_areas', {
          sort: 'name',
          sort_direction: 'asc',
        });

        const response2 = await api.as(admin).get('/api/v1/work_areas', {
          sort: 'name',
          sort_direction: 'asc',
        });

        expect(response1.status).toBe(200);
        expect(response2.status).toBe(200);
        expect(response1.body.data).toEqual(response2.body.data);
      });
    });
  });
});
