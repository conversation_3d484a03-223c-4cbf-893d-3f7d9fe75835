'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('job_vacancy_work_areas', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      job_vacancy_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'job_vacancies',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      work_area_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'work_areas',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      deleted_at: {
        type: Sequelize.DATE,
        allowNull: true,
      },
    });

    await queryInterface.addIndex('job_vacancy_work_areas', ['job_vacancy_id', 'work_area_id'], {
      unique: true,
      name: 'job_vacancy_work_areas_jv_id_wa_id_unique_constraint',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('job_vacancy_work_areas');
  },
};
