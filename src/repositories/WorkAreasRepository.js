const { Op } = require('sequelize');
const BaseRepository = require('./BaseRepository');
const { WorkArea } = require('../models');

class WorkAreasRepository extends BaseRepository {
  constructor() {
    super(WorkArea);
  }

  filterBySearch(search) {
    if (!search) return null;

    return {
      name: {
        [Op.iLike]: `%${search}%`,
      },
    };
  }
}

module.exports = WorkAreasRepository;
