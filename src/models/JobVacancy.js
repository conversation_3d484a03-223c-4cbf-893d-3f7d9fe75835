'use strict';
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class JobVacancy extends AppModel {
  static associate(models) {
    this.belongsTo(models.JobTitle, {
      foreignKey: 'job_title_id',
      as: 'jobTitle',
    });
    this.belongsTo(models.JobLevel, {
      foreignKey: 'job_level_id',
      as: 'jobLevel',
    });
    this.belongsTo(models.Bone, {
      foreignKey: 'bone_id',
      as: 'bone',
    });
    this.belongsTo(models.WorkArea, {
      foreignKey: 'work_area_id',
      as: 'workArea',
    });
    this.hasMany(models.UserJobVacancy, {
      foreignKey: 'job_vacancy_id',
      as: 'userRecommendations',
    });
    this.hasMany(models.VacancyGroupVariable, {
      foreignKey: 'job_vacancy_id',
      as: 'vacancyGroupVariables',
    });
    this.hasMany(models.JobVacancyWorkArea, {
      foreignKey: 'job_vacancy_id',
      as: 'jobVacancyWorkAreas',
    });
    this.belongsToMany(models.WorkArea, {
      through: models.JobVacancyWorkArea,
      foreignKey: 'job_vacancy_id',
      otherKey: 'work_area_id',
      as: 'workAreas',
    });
  }

  static schema() {
    return {
      status: {
        type: DataTypes.STRING,
        validate: {
          isIn: [
            [
              'draft',
              'active',
              'generating_jobdesc',
              'generating_job_variables',
              'calculating_match_scores',
            ],
          ],
        },
      },
    };
  }

  static options() {
    return {
      tableName: 'job_vacancies',
    };
  }
}

module.exports = JobVacancy;
